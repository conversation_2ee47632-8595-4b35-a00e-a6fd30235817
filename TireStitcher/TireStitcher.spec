# -*- mode: python ; coding: utf-8 -*-
from PyInstaller.utils.hooks import collect_all

datas = [('../extract_frames.exe', '.'), ('../stitch_tire.exe', '.'), ('../libasprintf-0.dll', '.'), ('../libatomic-1.dll', '.'), ('../libcharset-1.dll', '.'), ('../libgcc_s_dw2-1.dll', '.'), ('../libgcc_s_seh-1.dll', '.'), ('../libgmp-10.dll', '.'), ('../libgmpxx-4.dll', '.'), ('../libgomp-1.dll', '.'), ('../libiconv-2.dll', '.'), ('../libintl-8.dll', '.'), ('../libisl-23.dll', '.'), ('../libmpc-3.dll', '.'), ('../libmpfr-6.dll', '.'), ('../libopencv_calib3d455.dll', '.'), ('../libopencv_core455.dll', '.'), ('../libopencv_dnn455.dll', '.'), ('../libopencv_features2d455.dll', '.'), ('../libopencv_flann455.dll', '.'), ('../libopencv_gapi455.dll', '.'), ('../libopencv_highgui455.dll', '.'), ('../libopencv_imgcodecs455.dll', '.'), ('../libopencv_imgproc455.dll', '.'), ('../libopencv_ml455.dll', '.'), ('../libopencv_objdetect455.dll', '.'), ('../libopencv_photo455.dll', '.'), ('../libopencv_stitching455.dll', '.'), ('../libopencv_video455.dll', '.'), ('../libopencv_videoio455.dll', '.'), ('../libquadmath-0.dll', '.'), ('../libssp-0.dll', '.'), ('../libstdc++-6.dll', '.'), ('../libwinpthread-1.dll', '.'), ('../libzstd.dll', '.'), ('../opencv_videoio_ffmpeg455_64.dll', '.'), ('../zlib1.dll', '.'), ('ffmpeg_bin/ffmpeg.exe', 'ffmpeg_bin'), ('ffmpeg_bin/ffplay.exe', 'ffmpeg_bin'), ('ffmpeg_bin/ffprobe.exe', 'ffmpeg_bin'), ('resources', 'resources'), ('templates', 'templates')]
binaries = []
hiddenimports = ['PIL', 'cv2', 'numpy', 'customtkinter', 'ttkthemes']
tmp_ret = collect_all('cv2')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]
tmp_ret = collect_all('numpy')
datas += tmp_ret[0]; binaries += tmp_ret[1]; hiddenimports += tmp_ret[2]


a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=binaries,
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='TireStitcher',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)
coll = COLLECT(
    exe,
    a.binaries,
    a.datas,
    strip=False,
    upx=True,
    upx_exclude=[],
    name='TireStitcher',
)
