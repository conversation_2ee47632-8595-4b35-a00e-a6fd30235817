"""
Plans tab for the Tire Panorama Tool.

Displays and manages test plans.
"""
import tkinter as tk
from tkinter import ttk, messagebox

def create_plans_tab(self):
    """
    Create the plans tab.

    Args:
        self: TirePanoramaApp instance
    """
    # Create frame for the plans tab
    content_frame = ttk.Frame(self.plans_tab)
    content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Split into two panels
    left_panel = ttk.Frame(content_frame)
    right_panel = ttk.Frame(content_frame)

    left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
    right_panel.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))

    # Plans List (left panel)
    _create_plans_list(self, left_panel)

    # Plan Details and Actions (right panel)
    _create_plan_details(self, right_panel)

    # Load plans
    update_plans_list(self)

def _create_plans_list(self, parent):
    """Create the plans list"""
    # Frame for the plans list
    list_frame = ttk.LabelFrame(parent, text="Test Plans")
    list_frame.pack(fill=tk.BOTH, expand=True)

    # Create treeview for plans
    columns = ("name",)
    self.plans_tree = ttk.Treeview(
        list_frame,
        columns=columns,
        show="headings",
        selectmode="extended"  # Changed from "browse" to "extended" to allow multiple selection
    )

    # Configure columns
    self.plans_tree.heading("name", text="Template Name")
    self.plans_tree.column("name", width=300, anchor=tk.W)

    # Add scrollbar
    scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.plans_tree.yview)
    self.plans_tree.configure(yscrollcommand=scrollbar.set)

    # Pack widgets
    self.plans_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

    # Bind selection event
    self.plans_tree.bind("<<TreeviewSelect>>", lambda event: on_plan_select(self))

    # Bind keyboard events for Ctrl and Shift selection
    self.plans_tree.bind("<Control-ButtonRelease-1>", lambda event: on_plan_ctrl_click(self, event))
    self.plans_tree.bind("<Shift-ButtonRelease-1>", lambda event: on_plan_shift_click(self, event))

    # Bind Delete key for deleting plans
    self.plans_tree.bind("<Delete>", lambda event: delete_plan(self))
    # For German keyboards (Entf key)
    self.plans_tree.bind("<KP_Delete>", lambda event: delete_plan(self))

def _create_plan_details(self, parent):
    """Create the plan details and actions panel"""
    # Plan Details Frame
    details_frame = ttk.LabelFrame(parent, text="Plan Details", width=300)
    details_frame.pack(fill=tk.X, padx=5, pady=5)

    # Plan name
    name_frame = ttk.Frame(details_frame)
    name_frame.pack(fill=tk.X, padx=5, pady=5)

    ttk.Label(name_frame, text="Name:").pack(side=tk.LEFT)
    self.plan_name_var = tk.StringVar()
    name_entry = ttk.Entry(name_frame, textvariable=self.plan_name_var, width=25)
    name_entry.pack(side=tk.LEFT, padx=5, fill=tk.X, expand=True)

    # Plan description (optional)
    description_frame = ttk.Frame(details_frame)
    description_frame.pack(fill=tk.X, padx=5, pady=5)

    ttk.Label(description_frame, text="Description:").pack(anchor=tk.W)
    self.plan_description_var = tk.StringVar()
    ttk.Entry(description_frame, textvariable=self.plan_description_var).pack(fill=tk.X, pady=2)

    # Add some vertical space
    ttk.Frame(details_frame, height=10).pack(fill=tk.X)

    # Plan information text
    info_text = ("Plans categorize tire projects for easier organization.\n\n"
                 "Create plans for different types of tires you commonly test, "
                 "such as 'Car Tires', 'SUV Tires', or 'Truck Tires'.")

    info_label = ttk.Label(
        details_frame,
        text=info_text,
        wraplength=280,
        justify=tk.LEFT,
        foreground="#555555"
    )
    info_label.pack(fill=tk.X, padx=5, pady=5)

    # Action buttons frame
    actions_frame = ttk.Frame(parent)
    actions_frame.pack(fill=tk.X, padx=5, pady=5)

    # Plan action buttons
    self.new_plan_btn = ttk.Button(
        actions_frame, text="New Plan", command=lambda: new_plan(self)
    )
    self.save_plan_btn = ttk.Button(
        actions_frame, text="Save Plan", command=lambda: save_plan(self)
    )
    self.delete_plan_btn = ttk.Button(
        actions_frame, text="Delete Plan", command=lambda: delete_plan(self)
    )

    self.new_plan_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
    self.save_plan_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)
    self.delete_plan_btn.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=2)

    # Disable buttons initially
    self.save_plan_btn.config(state=tk.DISABLED)
    self.delete_plan_btn.config(state=tk.DISABLED)

    # Projects using this plan frame
    projects_frame = ttk.LabelFrame(parent, text="Projects Using This Plan")
    projects_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Create listbox for projects
    self.plan_projects_listbox = tk.Listbox(
        projects_frame,
        height=8,
        **self.listbox_config
    )

    # Add scrollbar
    projects_scrollbar = ttk.Scrollbar(
        projects_frame,
        orient=tk.VERTICAL,
        command=self.plan_projects_listbox.yview
    )
    self.plan_projects_listbox.configure(yscrollcommand=projects_scrollbar.set)

    # Pack widgets
    self.plan_projects_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    projects_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

def update_plans_list(self):
    """Update the plans list in the treeview"""
    # Clear the list
    self.plans_tree.delete(*self.plans_tree.get_children())

    # Add plans
    for plan in self.plans:
        self.plans_tree.insert(
            "", "end",
            values=(plan.name,),
            tags=(plan.id,)
        )

def on_plan_select(self):
    """Handle plan selection"""
    selection = self.plans_tree.selection()

    # Clear the selected plans list
    self.selected_plans = []

    if not selection:
        # No selection, disable buttons
        self.save_plan_btn.config(state=tk.DISABLED)
        self.delete_plan_btn.config(state=tk.DISABLED)

        # Clear fields
        self.plan_name_var.set("")
        self.plan_description_var.set("")

        # Clear projects list
        self.plan_projects_listbox.delete(0, tk.END)

        # Clear current selection
        self.current_plan = None

        return

    # Process all selected items
    for item_id in selection:
        # Get the plan ID from the item's tags
        plan_id = self.plans_tree.item(item_id, "tags")[0]

        # Find the plan
        for t in self.plans:
            if t.id == plan_id:
                self.selected_plans.append(t)
                break

    if not self.selected_plans:
        return

    # Use the first selected plan for the details panel
    self.current_plan = self.selected_plans[0]

    # Enable buttons if we have at least one plan selected
    self.save_plan_btn.config(state=tk.NORMAL)
    self.delete_plan_btn.config(state=tk.NORMAL)

    # Update fields with the first selected plan
    self.plan_name_var.set(self.current_plan.name)

    # Use unique_attribute as a description field
    description = ""
    if hasattr(self.current_plan, 'unique_attribute') and self.current_plan.unique_attribute:
        description = self.current_plan.unique_attribute
    self.plan_description_var.set(description)

    # Update projects list for the first selected plan
    update_plan_projects_list(self)

def update_plan_projects_list(self):
    """Update the list of projects using the current plan"""
    # Clear the list
    self.plan_projects_listbox.delete(0, tk.END)

    if not self.current_plan:
        return

    # Get projects using this plan
    projects = self.project_manager.get_projects_by_plan(self.current_plan.id)

    if not projects:
        self.plan_projects_listbox.insert(tk.END, "No projects using this plan")
        return

    # Add projects to the list
    for project in projects:
        self.plan_projects_listbox.insert(tk.END, project.serial_number)

def new_plan(self):
    """Create a new plan"""
    # Clear selection and fields
    self.plans_tree.selection_remove(self.plans_tree.selection())

    self.plan_name_var.set("New Test Plan")
    self.plan_description_var.set("")

    # Enable save button
    self.save_plan_btn.config(state=tk.NORMAL)

    # Disable delete button
    self.delete_plan_btn.config(state=tk.DISABLED)

    # Set current plan to None
    self.current_plan = None

def save_plan(self):
    """Save the current plan"""
    # Get values from fields
    name = self.plan_name_var.get().strip()
    description = self.plan_description_var.get().strip()

    if not name:
        messagebox.showerror("Error", "Plan name cannot be empty")
        return

    try:
        if self.current_plan:
            # Update existing plan
            self.plan_manager.update_plan(
                self.current_plan.id,
                name=name,
                unique_attribute=description
            )
            status_message = f"Plan '{name}' updated"
        else:
            # Create new plan
            from models.test_plan import TestPlan

            new_plan = TestPlan(
                name=name,
                unique_attribute=description
            )

            self.plan_manager.add_plan(new_plan)
            self.current_plan = new_plan
            status_message = f"Plan '{name}' created"

        # Reload plans
        self.plans = self.plan_manager.load_plans()
        update_plans_list(self)

        # Update status
        self.status_var.set(status_message)

        # Enable all buttons
        self.save_plan_btn.config(state=tk.NORMAL)
        self.delete_plan_btn.config(state=tk.NORMAL)

    except ValueError as e:
        messagebox.showerror("Error", f"Invalid value: {e}")

def on_plan_ctrl_click(self, event):
    """Handle Ctrl+click for multiple selection"""
    # The Treeview widget handles the selection automatically with selectmode="extended"
    # We just need to update our internal state
    on_plan_select(self)

def on_plan_shift_click(self, event):
    """Handle Shift+click for range selection"""
    # The Treeview widget handles the selection automatically with selectmode="extended"
    # We just need to update our internal state
    on_plan_select(self)

def delete_plan(self):
    """Delete the selected plans"""
    if not self.selected_plans:
        return

    # If only one plan is selected
    if len(self.selected_plans) == 1:
        plan = self.selected_plans[0]

        # Check if plan is used by any projects
        projects = self.project_manager.get_projects_by_plan(plan.id)

        if projects:
            # Ask if user wants to delete the plan AND all associated projects
            if not messagebox.askyesno(
                "Warning: Projects Will Be Deleted",
                f"Plan '{plan.name}' is used by {len(projects)} projects.\n\n"
                f"If you delete this plan, ALL {len(projects)} projects using it will also be deleted.\n\n"
                "This will permanently delete all project data, including panoramas.\n\n"
                "Are you sure you want to delete this plan and all its projects?",
                icon=messagebox.WARNING
            ):
                return

            # User confirmed deletion of plan and projects
            # First delete all projects using this plan
            deleted_projects = 0
            for project in projects:
                if self.project_manager.delete_project(project.id):
                    deleted_projects += 1

                # Clear current project if it's one that was deleted
                if hasattr(self, 'current_project') and self.current_project and self.current_project.id == project.id:
                    self.current_project = None
        else:
            # No projects using this plan, just confirm deletion
            if not messagebox.askyesno(
                "Confirm Deletion",
                f"Are you sure you want to delete plan '{plan.name}'?"
            ):
                return

        # Delete plan
        self.plan_manager.delete_plan(plan.id)

        # Reload plans
        self.plans = self.plan_manager.load_plans()
        update_plans_list(self)

        # Clear selection
        self.plans_tree.selection_remove(self.plans_tree.selection())

        # Update status
        if projects:
            self.status_var.set(f"Plan '{plan.name}' and {deleted_projects} projects deleted")
        else:
            self.status_var.set(f"Plan '{plan.name}' deleted")

        # Reset current plan
        self.current_plan = None
        self.selected_plans = []

        # Disable buttons
        self.save_plan_btn.config(state=tk.DISABLED)
        self.delete_plan_btn.config(state=tk.DISABLED)

        # Update projects list
        from gui.tabs.projects_tab import update_projects_list
        update_projects_list(self)

        # Update current project display if it exists
        if hasattr(self, 'update_current_project_display'):
            self.update_current_project_display()
    else:
        # Multiple plans selected
        # Check if any plans are used by projects
        plans_with_projects = []
        total_projects = 0

        for plan in self.selected_plans:
            projects = self.project_manager.get_projects_by_plan(plan.id)
            if projects:
                plans_with_projects.append((plan, len(projects)))
                total_projects += len(projects)

        # Prepare confirmation message
        plan_names = ", ".join([t.name for t in self.selected_plans])

        if plans_with_projects:
            # Some plans have associated projects
            warning_msg = f"You are about to delete {len(self.selected_plans)} plans.\n\n"
            warning_msg += "The following plans have associated projects that will also be deleted:\n\n"

            for plan, count in plans_with_projects:
                warning_msg += f"- '{plan.name}' is used by {count} projects\n"

            warning_msg += f"\nIn total, {total_projects} projects will be permanently deleted, including all panoramas.\n\n"
            warning_msg += "Are you sure you want to delete these plans and all their projects?"

            if not messagebox.askyesno("Warning: Projects Will Be Deleted", warning_msg, icon=messagebox.WARNING):
                return

            # User confirmed deletion of plans and projects
            # First delete all projects using these plans
            deleted_projects = 0
            for plan in self.selected_plans:
                projects = self.project_manager.get_projects_by_plan(plan.id)
                for project in projects:
                    if self.project_manager.delete_project(project.id):
                        deleted_projects += 1

                    # Clear current project if it's one that was deleted
                    if hasattr(self, 'current_project') and self.current_project and self.current_project.id == project.id:
                        self.current_project = None
        else:
            # No plans have associated projects
            if not messagebox.askyesno(
                "Confirm Deletion",
                f"Are you sure you want to delete {len(self.selected_plans)} plans?\n\n{plan_names}"
            ):
                return

        # Delete all plans
        for plan in self.selected_plans:
            self.plan_manager.delete_plan(plan.id)

        # Reload plans
        self.plans = self.plan_manager.load_plans()
        update_plans_list(self)

        # Clear selection
        self.plans_tree.selection_remove(self.plans_tree.selection())

        # Update status
        if plans_with_projects:
            self.status_var.set(f"{len(self.selected_plans)} plans and {deleted_projects} projects deleted")
        else:
            self.status_var.set(f"{len(self.selected_plans)} plans deleted")

        # Reset current plan
        self.current_plan = None
        self.selected_plans = []

        # Disable buttons
        self.save_plan_btn.config(state=tk.DISABLED)
        self.delete_plan_btn.config(state=tk.DISABLED)

        # Update projects list
        from gui.tabs.projects_tab import update_projects_list
        update_projects_list(self)

        # Update current project display if it exists
        if hasattr(self, 'update_current_project_display'):
            self.update_current_project_display()