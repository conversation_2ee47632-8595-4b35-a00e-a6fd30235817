"""
Projects tab for the Tire Panorama Tool.

Displays and manages tire projects (gallery).
"""
import os
import tkinter as tk
from tkinter import ttk, messagebox
from datetime import datetime

def create_projects_tab(self):
    """
    Create the projects tab.

    Args:
        self: TirePanoramaApp instance
    """
    # Create frame for the projects tab
    content_frame = ttk.Frame(self.projects_tab)
    content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

    # Split into two panels
    left_panel = ttk.Frame(content_frame)
    # Create right panel with fixed width and prevent resizing
    self.right_panel_container = ttk.Frame(content_frame, width=300)
    self.right_panel_container.pack_propagate(False)  # This prevents the frame from shrinking

    left_panel.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 5))
    self.right_panel_container.pack(side=tk.RIGHT, fill=tk.Y, padx=(5, 0))

    # Projects Tree (left panel)
    _create_projects_tree(self, left_panel)

    # Create empty right panel initially
    _create_empty_right_panel(self, self.right_panel_container)

    # Store reference to update function
    self.update_projects_list = update_projects_list

    # Load projects
    update_projects_list(self)

def _create_empty_right_panel(self, parent):
    """Create an entirely empty right panel"""
    # Store the empty right panel frame
    self.empty_right_panel = ttk.Frame(parent)
    self.empty_right_panel.pack(fill=tk.BOTH, expand=True)

    # The panel is completely empty - no message, no buttons

def _create_project_details(self, parent):
    """Create the project details and actions panel"""
    # Project Details Frame
    self.project_details_panel = ttk.Frame(parent)

    details_frame = ttk.LabelFrame(self.project_details_panel, text="Project Details", width=300)
    details_frame.pack(fill=tk.X, padx=5, pady=5)

    # Serial Number
    name_frame = ttk.Frame(details_frame)
    name_frame.pack(fill=tk.X, padx=5, pady=5)

    ttk.Label(name_frame, text="Serial Number:", width=12, anchor=tk.W).pack(side=tk.LEFT)
    self.project_serial_var = tk.StringVar()
    ttk.Label(name_frame, textvariable=self.project_serial_var, style="Value.TLabel").pack(side=tk.LEFT, fill=tk.X, expand=True)



    # Plan
    plan_frame = ttk.Frame(details_frame)
    plan_frame.pack(fill=tk.X, padx=5, pady=5)

    ttk.Label(plan_frame, text="Plan:", width=12, anchor=tk.W).pack(side=tk.LEFT)
    self.project_plan_var = tk.StringVar()
    ttk.Label(plan_frame, textvariable=self.project_plan_var, style="Value.TLabel").pack(side=tk.LEFT, fill=tk.X, expand=True)

    # Created date
    date_frame = ttk.Frame(details_frame)
    date_frame.pack(fill=tk.X, padx=5, pady=5)

    ttk.Label(date_frame, text="Created:", width=12, anchor=tk.W).pack(side=tk.LEFT)
    self.project_date_var = tk.StringVar()
    ttk.Label(date_frame, textvariable=self.project_date_var, style="Value.TLabel").pack(side=tk.LEFT, fill=tk.X, expand=True)

    # Dimensions frame (moved from template to project)
    dimensions_frame = ttk.LabelFrame(self.project_details_panel, text="Tire Dimensions")
    dimensions_frame.pack(fill=tk.X, padx=5, pady=5)

    # Add dimension fields
    dim_width_frame = ttk.Frame(dimensions_frame)
    dim_width_frame.pack(fill=tk.X, padx=5, pady=2)
    ttk.Label(dim_width_frame, text="Width:", width=12, anchor=tk.W).pack(side=tk.LEFT)
    self.project_width_var = tk.StringVar()
    ttk.Label(dim_width_frame, textvariable=self.project_width_var).pack(side=tk.LEFT)
    ttk.Button(dim_width_frame, text="Edit", width=6,
              command=lambda: edit_project_dimension(self, 'width')).pack(side=tk.RIGHT)

    dim_diameter_frame = ttk.Frame(dimensions_frame)
    dim_diameter_frame.pack(fill=tk.X, padx=5, pady=2)
    ttk.Label(dim_diameter_frame, text="Diameter:", width=12, anchor=tk.W).pack(side=tk.LEFT)
    self.project_diameter_var = tk.StringVar()
    ttk.Label(dim_diameter_frame, textvariable=self.project_diameter_var).pack(side=tk.LEFT)
    ttk.Button(dim_diameter_frame, text="Edit", width=6,
              command=lambda: edit_project_dimension(self, 'diameter')).pack(side=tk.RIGHT)

    dim_circum_frame = ttk.Frame(dimensions_frame)
    dim_circum_frame.pack(fill=tk.X, padx=5, pady=2)
    ttk.Label(dim_circum_frame, text="Circumference:", width=12, anchor=tk.W).pack(side=tk.LEFT)
    self.project_circumference_var = tk.StringVar()
    ttk.Label(dim_circum_frame, textvariable=self.project_circumference_var).pack(side=tk.LEFT)
    ttk.Button(dim_circum_frame, text="Edit", width=6,
              command=lambda: edit_project_dimension(self, 'circumference')).pack(side=tk.RIGHT)

    # Subproject status summary frame
    status_frame = ttk.LabelFrame(self.project_details_panel, text="Subprojects")
    status_frame.pack(fill=tk.X, padx=5, pady=5)

    # Create a frame for each subproject with progress indicators
    for subproject_type in ["Frontal", "Left Side", "Right Side"]:
        subproject_frame = ttk.Frame(status_frame)
        subproject_frame.pack(fill=tk.X, padx=5, pady=5)

        ttk.Label(subproject_frame, text=f"{subproject_type}:", width=12, anchor=tk.W).pack(side=tk.LEFT)

        # Variable to store status text
        var_name = f"project_{subproject_type.lower().replace(' ', '_')}_status_var"
        setattr(self, var_name, tk.StringVar(value="Not started"))
        status_label = ttk.Label(
            subproject_frame,
            textvariable=getattr(self, var_name),
            style="Status.TLabel"
        )
        status_label.pack(side=tk.LEFT, fill=tk.X, expand=True)

    # Actions frame
    actions_frame = ttk.LabelFrame(self.project_details_panel, text="Actions")
    actions_frame.pack(fill=tk.X, padx=5, pady=5)

    # Open button
    self.open_project_btn = ttk.Button(
        actions_frame,
        text="Open Project",
        command=lambda: open_selected_project(self)
    )
    self.open_project_btn.pack(fill=tk.X, padx=5, pady=5)

    # Update Serial Number button
    self.rename_project_btn = ttk.Button(
        actions_frame,
        text="Update Serial Number",
        command=lambda: rename_selected_project(self)
    )
    self.rename_project_btn.pack(fill=tk.X, padx=5, pady=2)

    # Delete button
    self.delete_project_btn = ttk.Button(
        actions_frame,
        text="Delete Project",
        command=lambda: delete_selected_project(self)
    )
    self.delete_project_btn.pack(fill=tk.X, padx=5, pady=2)

def _create_projects_tree(self, parent):
    """Create the projects tree with grouping by plan"""
    # Frame for the projects tree
    tree_frame = ttk.LabelFrame(parent, text="Tire Projects Gallery")
    tree_frame.pack(fill=tk.BOTH, expand=True)

    # Create treeview for projects
    self.projects_tree = ttk.Treeview(
        tree_frame,
        columns=("name", "date", "status", "dimensions"),
        show="tree headings",
        selectmode="extended"  # Changed from "browse" to "extended" to allow multiple selection
    )

    # Configure columns
    self.projects_tree.heading("name", text="Serial Number")
    self.projects_tree.heading("date", text="Created Date")
    self.projects_tree.heading("status", text="Status")
    self.projects_tree.heading("dimensions", text="Dimensions")

    self.projects_tree.column("#0", width=30)  # Tree expand/collapse icons
    self.projects_tree.column("name", width=250, anchor=tk.W)
    self.projects_tree.column("date", width=150, anchor=tk.CENTER)
    self.projects_tree.column("status", width=100, anchor=tk.CENTER)
    self.projects_tree.column("dimensions", width=150, anchor=tk.CENTER)

    # Add scrollbars
    v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.projects_tree.yview)
    h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.projects_tree.xview)
    self.projects_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

    # Pack widgets
    self.projects_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
    v_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    h_scrollbar.pack(side=tk.BOTTOM, fill=tk.X)

    # Define style for plan headers (bold font)
    style = ttk.Style()
    style.configure("Plan.Treeview.Item", font=("TkDefaultFont", 10, "bold"))

    # Bind selection event
    self.projects_tree.bind("<<TreeviewSelect>>", lambda event: on_project_select(self))

    # Bind keyboard events for Ctrl and Shift selection
    self.projects_tree.bind("<Control-ButtonRelease-1>", lambda event: on_project_ctrl_click(self, event))
    self.projects_tree.bind("<Shift-ButtonRelease-1>", lambda event: on_project_shift_click(self, event))

    # Bind Delete key for deleting projects
    self.projects_tree.bind("<Delete>", lambda event: delete_selected_project(self))
    # For German keyboards (Entf key)
    self.projects_tree.bind("<KP_Delete>", lambda event: delete_selected_project(self))

    # Create new project button
    new_project_btn = ttk.Button(
        parent,
        text="Create New Project",
        command=lambda: create_new_project(self)
    )
    new_project_btn.pack(fill=tk.X, pady=5)

def _show_project_details_panel(self):
    """Show the project details panel"""
    # Hide empty panel
    if hasattr(self, 'empty_right_panel') and self.empty_right_panel.winfo_ismapped():
        self.empty_right_panel.pack_forget()

    # Create project details panel if it doesn't exist
    if not hasattr(self, 'project_details_panel'):
        _create_project_details(self, self.right_panel_container)

    # Show project details panel
    if hasattr(self, 'project_details_panel') and not self.project_details_panel.winfo_ismapped():
        self.project_details_panel.pack(fill=tk.BOTH, expand=True)

    # Enable all buttons in the panel
    self.open_project_btn.config(state=tk.NORMAL)
    self.rename_project_btn.config(state=tk.NORMAL)
    self.delete_project_btn.config(state=tk.NORMAL)

def _hide_project_details_panel(self):
    """Hide the project details panel and show the empty panel"""
    # Hide project details panel if it exists
    if hasattr(self, 'project_details_panel') and self.project_details_panel.winfo_ismapped():
        self.project_details_panel.pack_forget()

    # Show empty panel
    if hasattr(self, 'empty_right_panel') and not self.empty_right_panel.winfo_ismapped():
        self.empty_right_panel.pack(fill=tk.BOTH, expand=True)

def update_projects_list(self):
    """Update the projects tree"""
    # Clear the tree
    self.projects_tree.delete(*self.projects_tree.get_children())

    # Reload projects
    self.projects = self.project_manager.load_projects()

    # Create a mapping of plans to their projects
    plans_with_projects = {}
    for project in self.projects:
        plan_id = project.template_id
        if plan_id not in plans_with_projects:
            plans_with_projects[plan_id] = []
        plans_with_projects[plan_id].append(project)

    # Create plan nodes
    plan_nodes = {}

    # Only add plans that have associated projects
    for plan in self.plans:
        if plan.id in plans_with_projects:
            # Format columns
            name = plan.name
            node_id = f"plan_{plan.id}"
            plan_nodes[plan.id] = node_id

            # Add plan node with custom style for bold text
            self.projects_tree.insert(
                "", "end",
                iid=node_id,
                text="",
                values=(name, "", "", ""),
                open=True,
                tags=("plan",)
            )

            # Apply the bold text style to this item
            self.projects_tree.tag_configure("plan", font=("TkDefaultFont", 10, "bold"))

    # Add projects under plan groups
    for project in self.projects:
        # Format columns
        name = project.serial_number



        # Format date
        try:
            date_obj = datetime.fromisoformat(project.created_date)
            date = date_obj.strftime("%Y-%m-%d %H:%M")
        except:
            date = project.created_date

        # Format dimensions
        dimensions = ""
        if project.width:
            dimensions += f"W:{project.width}"
        if project.diameter:
            if dimensions:
                dimensions += ", "
            dimensions += f"D:{project.diameter:.1f}"

        # Get overall status
        status = get_project_status(project)

        # Insert under plan group
        plan_id = project.template_id
        parent_node = plan_nodes.get(plan_id, "")

        # If plan doesn't exist, create "Unknown" group
        if not parent_node:
            parent_node = "plan_unknown"
            if parent_node not in plan_nodes.values():
                self.projects_tree.insert(
                    "", "end",
                    iid=parent_node,
                    text="",
                    values=("Unknown Plan", "", "", ""),
                    open=True,
                    tags=("plan",)
                )
                plan_nodes["unknown"] = parent_node

        # Add project
        self.projects_tree.insert(
            parent_node, "end",
            values=(name, date, status, dimensions),
            tags=(f"project_{project.id}",)
        )

    # Hide project details if no project is selected
    if hasattr(self, 'project_details_panel'):
        _hide_project_details_panel(self)

def get_project_status(project):
    """Get overall status text for a project"""
    statuses = [subproject.get("status", "not_started") for subproject in project.subprojects.values()]

    if all(status == "completed" for status in statuses):
        return "Completed"
    elif any(status == "processing" for status in statuses):
        return "Processing"
    elif any(status == "cancelled" for status in statuses):
        return "Partially Done"
    elif any(status == "completed" for status in statuses):
        return "Partially Done"
    else:
        return "Not Started"

def on_project_select(self):
    """Handle project selection"""
    selection = self.projects_tree.selection()

    # Clear the selected projects list
    self.selected_projects = []

    if not selection:
        # No selection, hide details panel
        _hide_project_details_panel(self)

        # Clear current selection
        self.current_project = None

        return

    # Process all selected items
    has_plan_node = False
    for item_id in selection:
        item_tags = self.projects_tree.item(item_id, "tags")

        # Check if this is a plan node
        if "plan" in item_tags:
            has_plan_node = True
            continue

        # Get the project ID from the item's tags
        project_tag = [tag for tag in item_tags if tag.startswith("project_")]
        if not project_tag:
            continue

        project_id = project_tag[0].split("_", 1)[1]

        # Find the project
        for p in self.projects:
            if p.id == project_id:
                self.selected_projects.append(p)
                break

    # If we have plan nodes selected or no projects, hide details panel
    if has_plan_node or not self.selected_projects:
        _hide_project_details_panel(self)
        self.current_project = None
        return

    # Ensure the project details panel is created and visible
    _show_project_details_panel(self)

    # Use the first selected project for the details panel
    self.current_project = self.selected_projects[0]

    # Update fields with the first selected project
    self.project_serial_var.set(self.current_project.serial_number)



    # Get plan name
    plan_name = "Unknown"
    for plan in self.plans:
        if plan.id == self.current_project.template_id:
            plan_name = plan.name
            break

    self.project_plan_var.set(plan_name)

    # Format date
    try:
        date_obj = datetime.fromisoformat(self.current_project.created_date)
        date = date_obj.strftime("%Y-%m-%d %H:%M")
    except:
        date = self.current_project.created_date

    self.project_date_var.set(date)

    # Update dimension fields
    self.project_width_var.set(f"{self.current_project.width} mm" if self.current_project.width else "Not set")
    self.project_diameter_var.set(f"{self.current_project.diameter} mm" if self.current_project.diameter else "Not set")
    self.project_circumference_var.set(f"{self.current_project.circumference} mm" if self.current_project.circumference else "Not set")

    # Update subproject status
    frontal_status = format_subproject_status(self.current_project.subprojects.get("frontal", {}).get("status", "not_started"))
    left_status = format_subproject_status(self.current_project.subprojects.get("left", {}).get("status", "not_started"))
    right_status = format_subproject_status(self.current_project.subprojects.get("right", {}).get("status", "not_started"))

    self.project_frontal_status_var.set(frontal_status)
    self.project_left_side_status_var.set(left_status)
    self.project_right_side_status_var.set(right_status)

def format_subproject_status(status):
    """Format subproject status for display"""
    status_mapping = {
        "not_started": "Not Started",
        "processing": "Processing...",
        "cancelled": "Cancelled",
        "completed": "Completed",
        "failed": "Failed"
    }

    return status_mapping.get(status, status.title())

def edit_project_dimension(self, dimension_type):
    """
    Edit a project dimension value

    Args:
        dimension_type: Type of dimension ("width", "diameter", or "circumference")
    """
    if not self.current_project:
        return

    # Get current value
    current_value = ""
    if dimension_type == "width":
        current_value = str(self.current_project.width) if self.current_project.width else ""
    elif dimension_type == "diameter":
        current_value = str(self.current_project.diameter) if self.current_project.diameter else ""
    elif dimension_type == "circumference":
        current_value = str(self.current_project.circumference) if self.current_project.circumference else ""

    # Create a simple dialog for editing the value
    dialog = tk.Toplevel(self.root)
    dialog.title(f"Edit {dimension_type.title()}")
    dialog.geometry("300x120")
    dialog.transient(self.root)
    dialog.grab_set()

    # Make dialog modal
    dialog.focus_set()

    # Center dialog
    x = self.root.winfo_x() + (self.root.winfo_width() // 2) - (300 // 2)
    y = self.root.winfo_y() + (self.root.winfo_height() // 2) - (120 // 2)
    dialog.geometry(f"+{x}+{y}")

    # Create content
    content_frame = ttk.Frame(dialog, padding=10)
    content_frame.pack(fill=tk.BOTH, expand=True)

    # Label
    ttk.Label(
        content_frame,
        text=f"Enter {dimension_type} value (mm):"
    ).pack(pady=(0, 5))

    # Entry
    value_var = tk.StringVar(value=current_value)
    entry = ttk.Entry(content_frame, textvariable=value_var, width=20)
    entry.pack(pady=5)
    entry.focus_set()
    entry.select_range(0, tk.END)

    # Buttons
    buttons_frame = ttk.Frame(content_frame)
    buttons_frame.pack(fill=tk.X, pady=10)

    def on_cancel():
        dialog.destroy()

    def on_save():
        # Parse value
        try:
            value_str = value_var.get().strip()
            if not value_str:
                value = None
            else:
                value = float(value_str)

            # Update project
            if dimension_type == "width":
                self.project_manager.update_project(self.current_project.id, width=value)
            elif dimension_type == "diameter":
                self.project_manager.update_project(self.current_project.id, diameter=value)
            elif dimension_type == "circumference":
                self.project_manager.update_project(self.current_project.id, circumference=value)

            # Reload current project data
            self.current_project = self.project_manager.get_project(self.current_project.id)

            # Update display
            on_project_select(self)

            # Update projects list
            update_projects_list(self)

            dialog.destroy()
        except ValueError:
            messagebox.showerror("Error", f"Invalid {dimension_type} value. Please enter a number.", parent=dialog)

    ttk.Button(buttons_frame, text="Cancel", command=on_cancel).pack(side=tk.RIGHT, padx=5)
    ttk.Button(buttons_frame, text="Save", command=on_save, style="Accent.TButton").pack(side=tk.RIGHT, padx=5)

    # Bind Enter key
    entry.bind("<Return>", lambda event: on_save())
    dialog.bind("<Escape>", lambda event: on_cancel())

# Helper functions for button actions
def create_new_project(self):
    """Create a new project"""
    from gui.dialogs.project_dialog import show_plan_selection_dialog, show_new_project_dialog

    # Show plan selection dialog
    if not self.plans:
        messagebox.showerror("Error", "No plans available. Please create a plan first.")
        # Switch to plans tab
        self.notebook.select(self.plans_tab)
        return

    # Show plan selection dialog
    plan = show_plan_selection_dialog(self)

    if plan:
        # Show new project dialog
        project = show_new_project_dialog(self, plan)

        if project:
            # Update the projects list
            update_projects_list(self)

            # Select the new project (find its item in the tree)
            project_tag = f"project_{project.id}"

            for template_id in self.projects_tree.get_children():
                for item_id in self.projects_tree.get_children(template_id):
                    tags = self.projects_tree.item(item_id, "tags")
                    if project_tag in tags:
                        self.projects_tree.selection_set(item_id)
                        self.projects_tree.see(item_id)

                        # Force selection event
                        on_project_select(self)
                        break

            # Update status
            self.status_var.set(f"Project with S/N '{project.serial_number}' created")

def open_selected_project(self):
    """Open the selected project"""
    if not self.current_project:
        return

    # Set as current project
    self.current_project = self.project_manager.get_project(self.current_project.id)

    # Switch to current project tab
    self.notebook.select(self.current_project_tab)

    # Update the current project display
    if hasattr(self, 'update_current_project_display'):
        self.update_current_project_display()

    # Update status
    self.status_var.set(f"Project with S/N '{self.current_project.serial_number}' opened")

def rename_selected_project(self):
    """Rename the selected project"""
    from gui.dialogs.project_dialog import show_rename_project_dialog

    if not self.current_project:
        return

    # Show rename dialog
    if show_rename_project_dialog(self, self.current_project):
        # Update the projects list
        update_projects_list(self)

        # Update the current project display if it's open
        if hasattr(self, 'update_current_project_display'):
            self.update_current_project_display()

        # Update status
        self.status_var.set(f"Project serial number updated to '{self.current_project.serial_number}'")

def on_project_ctrl_click(self, event):
    """Handle Ctrl+click for multiple selection"""
    # The Treeview widget handles the selection automatically with selectmode="extended"
    # We just need to update our internal state
    on_project_select(self)

def on_project_shift_click(self, event):
    """Handle Shift+click for range selection"""
    # The Treeview widget handles the selection automatically with selectmode="extended"
    # We just need to update our internal state
    on_project_select(self)

def delete_selected_project(self):
    """Delete the selected projects"""
    if not self.selected_projects:
        return

    # If only one project is selected
    if len(self.selected_projects) == 1:
        project = self.selected_projects[0]

        # Get project directory size
        project_dir_size = get_directory_size(project.project_dir)
        size_str = format_size(project_dir_size)

        # Confirm deletion with size information
        if not messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete tire with S/N '{project.serial_number}'?\n\n"
            f"This project contains approximately {size_str} of data.\n\n"
            "All project data will be permanently deleted, including:\n"
            "- Panorama images\n"
            "- Extracted frames\n"
            "- Processing data\n\n"
            "This action cannot be undone.",
            icon=messagebox.WARNING
        ):
            return

        project_name = project.serial_number
        project_id = project.id

        # Delete the project
        if self.project_manager.delete_project(project_id):
            # Update the projects list
            update_projects_list(self)

            # Clear current project if it's the one that was deleted
            if hasattr(self, 'current_project') and self.current_project and self.current_project.id == project_id:
                self.current_project = None
                self.selected_projects = []

                # Update the current project display if it's open
                if hasattr(self, 'update_current_project_display'):
                    self.update_current_project_display()

            # Update status
            self.status_var.set(f"Tire with S/N '{project_name}' deleted ({size_str})")
        else:
            # Update status
            self.status_var.set(f"Failed to delete tire with S/N '{project_name}'")
    else:
        # Multiple projects selected
        project_names = ", ".join([p.serial_number for p in self.selected_projects])

        # Calculate total size of all selected projects
        total_size = 0
        for project in self.selected_projects:
            total_size += get_directory_size(project.project_dir)

        size_str = format_size(total_size)

        # Confirm deletion with size information
        if not messagebox.askyesno(
            "Confirm Deletion",
            f"Are you sure you want to delete {len(self.selected_projects)} tire projects?\n\n"
            f"Selected serial numbers: {project_names}\n\n"
            f"These tire projects contain approximately {size_str} of data.\n\n"
            "All project data will be permanently deleted, including:\n"
            "- Panorama images\n"
            "- Extracted frames\n"
            "- Processing data\n\n"
            "This action cannot be undone.",
            icon=messagebox.WARNING
        ):
            return

        # Delete all selected projects
        deleted_count = 0
        for project in self.selected_projects:
            if self.project_manager.delete_project(project.id):
                deleted_count += 1

                # Clear current project if it's one that was deleted
                if hasattr(self, 'current_project') and self.current_project and self.current_project.id == project.id:
                    self.current_project = None

        # Update the projects list
        update_projects_list(self)

        # Clear selected projects
        self.selected_projects = []

        # Update the current project display if it's open
        if hasattr(self, 'update_current_project_display'):
            self.update_current_project_display()

        # Update status
        if deleted_count == len(self.selected_projects):
            self.status_var.set(f"{deleted_count} tire projects deleted ({size_str})")
        else:
            self.status_var.set(f"{deleted_count} of {len(self.selected_projects)} tire projects deleted ({size_str})")

def get_directory_size(directory):
    """Get the size of a directory in bytes"""
    total_size = 0
    try:
        for dirpath, dirnames, filenames in os.walk(directory):
            for filename in filenames:
                file_path = os.path.join(dirpath, filename)
                if not os.path.islink(file_path):
                    total_size += os.path.getsize(file_path)
    except Exception as e:
        print(f"Error calculating directory size: {e}")
    return total_size

def format_size(size_bytes):
    """Format size in bytes to a human-readable string"""
    if size_bytes < 1024:
        return f"{size_bytes} bytes"
    elif size_bytes < 1024 * 1024:
        return f"{size_bytes / 1024:.1f} KB"
    elif size_bytes < 1024 * 1024 * 1024:
        return f"{size_bytes / (1024 * 1024):.1f} MB"
    else:
        return f"{size_bytes / (1024 * 1024 * 1024):.2f} GB"