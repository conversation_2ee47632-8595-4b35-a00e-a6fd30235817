"""
UI creation utilities for the Tire Panorama Tool.
"""
import tkinter as tk
from tkinter import ttk
from gui.sidebar import create_sidebar_content

# Import tab creation functions
from utils.cache_cleanup import add_rebuild_button
from utils.diagnostics import run_diagnostics_window
from gui.tabs.plans_tab import create_plans_tab
from gui.tabs.projects_tab import create_projects_tab
from gui.tabs.current_project_tab import create_current_project_tab

def setup_sidebar_styles(self):
    """Define styles for UI with sidebar"""
    style = ttk.Style()

    # Color definitions
    primary_orange = "#ffa500"       # Main color for sidebar
    secondary_orange = "#ffd280"     # Lighter, simpler yellow for buttons
    hover_orange = "#ffb733"         # Lighter orange for hover effects
    background_color = "#f5f5f5"     # Light background for the main area
    text_dark = "#333333"            # Dark gray for text
    text_light = "white"             # White for text on colored background

    # Set base theme
    if 'clam' in style.theme_names():
        style.theme_use('clam')

    # Main window background
    self.root.configure(background=background_color)

    # Frame styles
    style.configure("TFrame", background=background_color)
    style.configure("TLabelframe", background=background_color)
    style.configure("TLabelframe.Label",
                background=primary_orange,
                foreground=text_light,
                font=("Ubuntu", 9, "bold"))

    # Button styles for main area
    style.configure("TButton",
                background="#e0e0e0",
                foreground=text_dark,
                font=("Ubuntu", 9))

    # Accent button style
    style.configure("Accent.TButton",
                background=primary_orange,
                foreground=text_light,
                font=("Ubuntu", 10, "bold"))

    # Hover effects
    style.map("TButton",
            background=[("active", "#d0d0d0")],
            foreground=[("active", text_dark)])

    style.map("Accent.TButton",
            background=[("active", hover_orange)],
            foreground=[("active", text_light)])

    # Style for sidebar buttons (Tk buttons, not ttk)
    self.sidebar_btn_style = {
        'bg': secondary_orange,
        'fg': text_dark,
        'font': ('Ubuntu', 10),
        'bd': 0,  # No border
        'highlightthickness': 0,  # No highlighting
        'relief': 'flat',  # Flat style
        'padx': 10,
        'pady': 5,
        'width': 20,  # Fixed width
        'activebackground': hover_orange,
        'activeforeground': text_dark
    }

    # Label styles
    style.configure("TLabel",
                background=background_color,
                foreground=text_dark,
                font=("Ubuntu", 9))

    # Sidebar label style
    self.sidebar_label_style = {
        'bg': primary_orange,
        'fg': text_light,
        'font': ('Ubuntu', 10, 'bold'),
        'padx': 10,
        'pady': 5
    }

    # Entry styles
    style.configure("TEntry",
                fieldbackground="white",
                foreground=text_dark,
                font=("Ubuntu", 9))

    # Checkbox styles
    style.configure("TCheckbutton",
                background=background_color,
                foreground=text_dark,
                font=("Ubuntu", 9))

    # Sidebar checkbox style
    style.configure("Sidebar.TCheckbutton",
                background=primary_orange,
                foreground=text_light,
                font=("Ubuntu", 9))

    # Progressbar style
    style.configure("TProgressbar",
                background=primary_orange,
                troughcolor="#e0e0e0")

    # Listbox configuration
    self.listbox_config = {
        "background": "white",
        "foreground": text_dark,
        "selectbackground": primary_orange,
        "selectforeground": text_light,
        "font": ("Ubuntu", 9),
        "borderwidth": 1,
        "relief": "solid"
    }

    # Canvas configuration
    self.canvas_config = {
        "bg": "white",
        "highlightthickness": 1,
        "highlightbackground": primary_orange
    }

    # Notebook style (for tabs)
    style.configure("TNotebook",
                background=background_color,
                borderwidth=0)
    style.configure("TNotebook.Tab",
                background="#e0e0e0",
                foreground=text_dark,
                font=("Ubuntu", 9, "bold"),
                padding=[10, 5])
    style.map("TNotebook.Tab",
            background=[("selected", primary_orange)],
            foreground=[("selected", text_light)])

def create_ui(self):
    """Create a UI with collapsible sidebar and tabs"""
    # Main container
    main_container = ttk.Frame(self.root)
    main_container.pack(fill=tk.BOTH, expand=True)

    # Top bar in orange
    top_bar = tk.Frame(main_container, bg="#ffa500", height=40)
    top_bar.pack(fill=tk.X, side=tk.TOP)

    # Title in top bar
    title_label = tk.Label(top_bar, text="Tire Panorama Tool",
                        bg="#ffa500", fg="white",
                        font=("Ubuntu", 12, "bold"))
    title_label.pack(side=tk.LEFT, padx=20, pady=8)

    # Define a function to handle the button click
    def on_select_video_folder():
        if hasattr(self, 'select_video_folder'):
            try:
                self.select_video_folder()
            except Exception as e:
                print(f"Error in select_video_folder: {e}")
                if hasattr(self, 'status_var'):
                    self.status_var.set(f"Error: {str(e)}")

    # Add video folder selection button to the top bar
    self.select_video_folder_btn = ttk.Button(
        top_bar,
        text="Set Standard Path",
        command=on_select_video_folder
    )
    self.select_video_folder_btn.pack(side=tk.RIGHT, padx=10, pady=5)

    # Add tooltip to the button
    try:
        from utils.cache_cleanup import ToolTip
        ToolTip(self.select_video_folder_btn, "Set a standard folder path for file selection dialogs")
    except ImportError:
        pass  # Tooltip is not critical functionality

    # Add rebuild button to the top right
    self.rebuild_btn = add_rebuild_button(self, top_bar)
    self.rebuild_btn.pack(side=tk.RIGHT, padx=10, pady=5)

    # Add diagnostics button
    self.diagnostics_btn = ttk.Button(
        top_bar,
        text="Diagnostics",
        command=lambda: run_diagnostics_window(self.root)
    )
    self.diagnostics_btn.pack(side=tk.RIGHT, padx=10, pady=5)

    # Add tooltip to the diagnostics button
    try:
        from utils.cache_cleanup import ToolTip
        ToolTip(self.diagnostics_btn, "Run diagnostics to troubleshoot issues")
    except ImportError:
        pass  # Tooltip is not critical functionality

    # Toggle button for sidebar
    self.sidebar_open = True  # Initially open
    self.toggle_btn = tk.Button(top_bar, text="≡", bg="#ffa500", fg="white",
                            font=("Ubuntu", 16), bd=0, padx=10,
                            activebackground="#e69400", activeforeground="white",
                            command=self.toggle_sidebar)
    self.toggle_btn.pack(side=tk.LEFT, padx=5)

    # Container for sidebar and main content
    self.content_container = ttk.Frame(main_container)
    self.content_container.pack(fill=tk.BOTH, expand=True)

    # Black separator line
    separator = tk.Frame(main_container, height=1, bg="black")
    separator.pack(fill=tk.X, side=tk.TOP)

    # Sidebar
    self.sidebar = tk.Frame(self.content_container, bg="#ffa500", width=250)
    self.sidebar.pack(fill=tk.Y, side=tk.LEFT)
    self.sidebar.pack_propagate(False)  # Prevents size changes

    # Main area (right side)
    self.main_content = ttk.Frame(self.content_container)
    self.main_content.pack(fill=tk.BOTH, expand=True, side=tk.RIGHT)

    # Sidebar contents
    create_sidebar_content(self)

    # Main content with tabs
    create_tabbed_content(self)

    # Status bar
    self.status_var = tk.StringVar(value="Ready")
    status_bar = ttk.Label(main_container, textvariable=self.status_var, relief=tk.SUNKEN, anchor=tk.W)
    status_bar.pack(side=tk.BOTTOM, fill=tk.X)

def create_tabbed_content(self):
    """Create tabbed interface for main content"""
    # Create notebook (tabbed interface)
    self.notebook = ttk.Notebook(self.main_content)
    self.notebook.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)

    # Create tabs
    self.plans_tab = ttk.Frame(self.notebook)
    self.projects_tab = ttk.Frame(self.notebook)
    self.current_project_tab = ttk.Frame(self.notebook)

    # Add tabs to notebook
    self.notebook.add(self.plans_tab, text="Test Plans")
    self.notebook.add(self.projects_tab, text="Tire Projects")
    self.notebook.add(self.current_project_tab, text="Current Project")

    # Create content for each tab
    create_plans_tab(self)
    create_projects_tab(self)
    create_current_project_tab(self)

    # Bind tab selection event - using lambda to ensure proper binding
    self.notebook.bind("<<NotebookTabChanged>>", lambda event: self.on_tab_selected(event))

def create_main_content(self):
    """Legacy method for backwards compatibility"""
    create_tabbed_content(self)